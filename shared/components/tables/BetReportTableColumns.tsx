// shared/components/tables/BetReportTableColumns.tsx
import { CopyToClipboard, SpkTableColumn, CurrencyDisplay } from "@/shared/UI/components";
import { StatusBadge } from "@/shared/UI/components";
import { BetReportData } from "@/shared/types/report-types";

// Helper function to format date
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Status styling is now handled by the StatusBadge component

interface BetReportTableColumnsOptions {
  onSettleBet?: (betId: string, record: BetReportData) => void;
}

export const getBetReportTableColumns = (options: BetReportTableColumnsOptions = {}): SpkTableColumn[] => {
  const { onSettleBet } = options;
  return [
    {
      key: "userName",
      title: "Username",
      sortable: false,
      width: "140px",
      render: (value, record: BetReportData) => (
        <div className="flex items-center gap-2">
          <span className="font-medium text-text-muted text-sm truncate">
            {value || record.userName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "createdAt",
      title: "Date & Time",
      sortable: false,
      width: "160px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {formatDate(value || record.createdAt)}
          </span>
        </div>
      )
    },
    {
      key: "marketId",
      title: "Market ID",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => {
        const marketId = value || record.marketId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-text-muted text-sm truncate">
              {marketId}
            </span>
            {marketId !== "N/A" && (
              <CopyToClipboard
                text={marketId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "betId",
      title: "Bet ID",
      sortable: false,
      width: "140px",
      render: (value, record: BetReportData) => {
        const betId = value || record.betId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm text-text-muted truncate">
              {betId}
            </span>
            {betId !== "N/A" && (
              <CopyToClipboard
                text={betId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "marketName",
      title: "Market Name",
      sortable: false,
      width: "180px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {value || record.marketName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "betType",
      title: "Bet Type",
      sortable: false,
      width: "100px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted capitalize truncate block">
            {value || record.betType || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "odds",
      title: "Odds",
      sortable: false,
      width: "80px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {value || record.odds || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "betAmount",
      title: "Bet Amount",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => (
        <div className="text-sm">
          <CurrencyDisplay
            amount={value || record.betAmount}
            context="table"
            size={14}
            amountClassName="font-medium text-text-muted truncate block"
            gap="sm"
          />
        </div>
      )
    },
    {
      key: "winAmount",
      title: "Win Amount",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => (
        <div className="text-sm text-green-600">
          <CurrencyDisplay
            amount={value || record.winAmount}
            context="table"
            size={14}
            amountClassName="font-medium truncate block"
            gap="sm"
          />
        </div>
      )
    },
    {
      key: "status",
      title: "Status",
      sortable: false,
      width: "120px",
      render: (value, record: BetReportData) => {
        const status = value || record.status || "unknown";
        // const normalizedStatus = status.toLowerCase();

        // For bet reports, show Win/Loss based on actual bet outcome
        // let displayStatus = status;
        // if (normalizedStatus === 'won') {
        //   displayStatus = 'Win';
        // } else if (normalizedStatus === 'lost') {
        //   displayStatus = 'Loss';
        // }

        return <StatusBadge status={status} />;
      }
    },
    {
      key: "settlement",
      title: "Settlement",
      sortable: false,
      width: "140px",
      render: (_, record: BetReportData) => {
        // Helper function to map numeric payoutStatus to string
        const mapPayoutStatus = (payoutStatus: any): string | undefined => {
          if (typeof payoutStatus === 'number') {
            switch (payoutStatus) {
              case 1: return 'settled';
              case 2: return 'rejected';
              case 3: return 'unsettled';
              default: return undefined;
            }
          }
          return typeof payoutStatus === 'string' ? payoutStatus.toLowerCase() : undefined;
        };

        // Use payoutSettlementStatus if available, otherwise fallback to status-based logic
        const payoutStatus = mapPayoutStatus(record.payoutStatus);
        const status = record.status?.toLowerCase() || "unknown";

        // Check if payout is settled or rejected
        if (payoutStatus === 'settled' || payoutStatus === 'rejected') {
          const displayText = payoutStatus === 'settled' ? 'Settled' : 'Rejected';
          const isSettled = payoutStatus === 'settled';

          return (
            <div className="text-sm">
              <span
                className="inline-flex items-center justify-between font-rubik font-normal text-sm leading-[100%]"
                style={{
                  background: isSettled ? '#83838333' : '#5B2424', // Gray for settled, red for rejected
                  color: isSettled ? '#999999' : '#FB3D32', // Gray text for settled, red text for rejected
                  padding: '8px 15px',
                  borderRadius: '4px',
                  gap: '8px',
                  minWidth: '100px',
                  width: "120px"
                }}
              >
                <span>{displayText}</span>
                <i
                  className={`ri-${isSettled ? 'check' : 'close'}-line`}
                  style={{ fontSize: '14px' }}
                />
              </span>
            </div>
          );
        }

        // Fallback to old logic for backward compatibility
        const isSettled = status === 'settled' || status === 'won' || status === 'lost';
        if (isSettled && !payoutStatus) {
          // Settled bets styling (legacy)
          return (
            <div className="text-sm">
              <span
                className="inline-flex items-center justify-between font-rubik font-normal text-sm leading-[100%]"
                style={{
                  background: '#83838333',
                  color: '#999999', // text-muted color from tailwind config
                  padding: '8px 15px',
                  borderRadius: '4px',
                  gap: '8px',
                  minWidth: '120px',
                }}
              >
                <span>Settled</span>
                <i
                  className="ri-check-line"
                  style={{ fontSize: '14px' }}
                />
              </span>
            </div>
          );
        }

        // Show Mark as Settled button for pending or unsettled bets
        const disabled = "opacity-50 cursor-auto";
        return (
          <div className="text-sm">
            <button
              onClick={() => record?.status == "Pending" ? "" : onSettleBet?.(record.betId, record)}
              className={`${record?.status == "Pending" ? disabled : ""} inline-flex items-center justify-center text-white font-rubik font-normal text-sm leading-[100%] transition-all duration-200 hover:shadow-lg`}
              style={{
                background: 'linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)',
                boxShadow: '4px 4px 8px 0px #FFFFFF40 inset, -4px -4px 8px 0px #916600 inset',
                padding: '8px 15px',
                borderRadius: '4px',
                gap: '8px',
                width: "120px",
                ...(record?.status == "Pending" ? { cursor: "auto !important" } : {})
              }}
            >
              Settle
            </button>
          </div>
        );
      }
    }, {
      key: "actions",
      title: "Bet Slip",
      sortable: false,
      width: "100px",
      render: (_, record: BetReportData) => {
        return <div title="print betSlip" className={`${record?.status == "Pending" ? "opacity-50 cursor-auto" : "cursor-pointer"} text-lg flex items-center justify-center`} onClick={record?.status == "Pending" ? () => { } : (e) => {
          e.stopPropagation();
          e.preventDefault();
          e.nativeEvent.stopImmediatePropagation();

        }}>
          <span className="text-text-muted truncate block">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_842_10770)">
                <path d="M12.8906 6.35938C12.1368 6.35938 11.5234 5.74605 11.5234 4.99219V0.5H4.53125C3.3466 0.5 2.38281 1.46379 2.38281 2.64844V18.3516C2.38281 19.5362 3.3466 20.5 4.53125 20.5H15.4688C16.6534 20.5 17.6172 19.5362 17.6172 18.3516V6.35938H12.8906ZM5.58594 14.5625H8.42656C8.75016 14.5625 9.0125 14.8248 9.0125 15.1484C9.0125 15.472 8.75016 15.7344 8.42656 15.7344H5.58594C5.26234 15.7344 5 15.472 5 15.1484C5 14.8248 5.26234 14.5625 5.58594 14.5625ZM5 12.0234C5 11.6998 5.26234 11.4375 5.58594 11.4375H14.1797C14.5033 11.4375 14.7656 11.6998 14.7656 12.0234C14.7656 12.347 14.5033 12.6094 14.1797 12.6094H5.58594C5.26234 12.6094 5 12.347 5 12.0234ZM14.1797 8.3125C14.5033 8.3125 14.7656 8.57484 14.7656 8.89844C14.7656 9.22203 14.5033 9.48438 14.1797 9.48438H5.58594C5.26234 9.48438 5 9.22203 5 8.89844C5 8.57484 5.26234 8.3125 5.58594 8.3125H14.1797Z" fill="#9FA3B6" />
                <path d="M12.6953 4.99216C12.6953 5.09986 12.7829 5.18747 12.8906 5.18747H17.356C17.2488 4.98958 17.1109 4.80782 16.9453 4.65107L13.1788 1.08771C13.0326 0.949426 12.8698 0.834075 12.6954 0.742981V4.99216H12.6953Z" fill="#9FA3B6" />
              </g>
              <defs>
                <clipPath id="clip0_842_10770">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
                </clipPath>
              </defs>
            </svg>

          </span>
        </div>;
      }
    }
  ];
};
